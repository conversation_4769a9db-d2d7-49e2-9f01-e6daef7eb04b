#!/usr/bin/env python3
"""
速度平滑处理脚本 - 方案A: 线性插值 + 移动平均
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

def linear_interpolation_smooth(data, transition_points=5):
    """
    线性插值平滑处理
    
    Args:
        data: 原始速度数据 (pandas Series)
        transition_points: 每个路段间的过渡点数量
    
    Returns:
        平滑后的速度数据
    """
    smoothed_data = []
    
    # 添加起点速度为0
    smoothed_data.append(0)
    
    i = 0
    while i < len(data) - 1:
        current_speed = data.iloc[i]
        next_speed = data.iloc[i + 1]
        
        # 如果速度相同，直接添加
        if current_speed == next_speed:
            smoothed_data.append(current_speed)
        else:
            # 线性插值过渡
            for j in range(transition_points):
                ratio = j / transition_points
                interpolated_speed = current_speed + (next_speed - current_speed) * ratio
                smoothed_data.append(interpolated_speed)
            
            # 添加目标速度
            smoothed_data.append(next_speed)
        
        i += 1
    
    # 添加终点速度为0
    if len(smoothed_data) > 0:
        # 从最后一个速度线性过渡到0
        last_speed = smoothed_data[-1]
        for j in range(1, transition_points + 1):
            ratio = j / transition_points
            interpolated_speed = last_speed * (1 - ratio)
            smoothed_data.append(interpolated_speed)
    
    smoothed_data.append(0)
    
    return np.array(smoothed_data)

def moving_average_smooth(data, window_size=5):
    """
    移动平均平滑处理
    
    Args:
        data: 输入数据
        window_size: 移动平均窗口大小
    
    Returns:
        平滑后的数据
    """
    if len(data) < window_size:
        return data
    
    # 使用pandas的rolling方法进行移动平均
    df = pd.DataFrame({'speed': data})
    smoothed = df['speed'].rolling(window=window_size, center=True, min_periods=1).mean()
    
    return smoothed.values

def process_speed_smoothing(input_csv, output_csv, transition_points=3, window_size=5):
    """
    完整的速度平滑处理流程
    
    Args:
        input_csv: 输入CSV文件路径
        output_csv: 输出CSV文件路径
        transition_points: 线性插值过渡点数量
        window_size: 移动平均窗口大小
    """
    
    # 读取原始数据
    df_original = pd.read_csv(input_csv, header=None, names=['时间', '速度'])
    print(f"原始数据点数: {len(df_original)}")
    print(f"原始速度范围: {df_original['速度'].min()} - {df_original['速度'].max()}")
    
    # 提取速度数据
    original_speeds = df_original['速度']
    
    # 步骤1: 线性插值平滑
    print("步骤1: 线性插值处理...")
    interpolated_speeds = linear_interpolation_smooth(original_speeds, transition_points)
    print(f"插值后数据点数: {len(interpolated_speeds)}")
    
    # 步骤2: 移动平均平滑
    print("步骤2: 移动平均处理...")
    final_smoothed_speeds = moving_average_smooth(interpolated_speeds, window_size)
    print(f"最终数据点数: {len(final_smoothed_speeds)}")
    print(f"平滑后速度范围: {final_smoothed_speeds.min():.2f} - {final_smoothed_speeds.max():.2f}")
    
    # 生成新的时间序列
    new_times = np.arange(1, len(final_smoothed_speeds) + 1)
    
    # 创建新的DataFrame
    df_smoothed = pd.DataFrame({
        '时间': new_times,
        '速度': final_smoothed_speeds
    })
    
    # 保存到CSV
    df_smoothed.to_csv(output_csv, index=False, header=False)
    print(f"平滑后数据已保存到: {output_csv}")
    
    return df_original, df_smoothed

def plot_comparison(df_original, df_smoothed, output_image='speed_comparison.png'):
    """
    绘制平滑前后的对比图
    
    Args:
        df_original: 原始数据DataFrame
        df_smoothed: 平滑后数据DataFrame
        output_image: 输出图片路径
    """
    
    plt.figure(figsize=(15, 10))
    
    # 子图1: 原始数据
    plt.subplot(3, 1, 1)
    plt.plot(df_original['时间'], df_original['速度'], 
             linewidth=1, color='red', alpha=0.8, label='原始速度')
    plt.title('原始速度曲线（阶跃函数）', fontsize=14)
    plt.xlabel('时间')
    plt.ylabel('速度')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 子图2: 平滑后数据
    plt.subplot(3, 1, 2)
    plt.plot(df_smoothed['时间'], df_smoothed['速度'], 
             linewidth=1, color='blue', alpha=0.8, label='平滑后速度')
    plt.title('平滑后速度曲线', fontsize=14)
    plt.xlabel('时间')
    plt.ylabel('速度')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 子图3: 对比图
    plt.subplot(3, 1, 3)
    
    # 为了对比，需要对原始数据进行采样或对平滑数据进行压缩
    if len(df_smoothed) > len(df_original):
        # 对平滑数据进行采样以匹配原始数据长度
        sample_indices = np.linspace(0, len(df_smoothed)-1, len(df_original), dtype=int)
        sampled_smoothed = df_smoothed.iloc[sample_indices]
        
        plt.plot(df_original['时间'], df_original['速度'], 
                linewidth=2, color='red', alpha=0.7, label='原始速度', linestyle='--')
        plt.plot(sampled_smoothed['时间'], sampled_smoothed['速度'], 
                linewidth=1.5, color='blue', alpha=0.8, label='平滑后速度')
    else:
        plt.plot(df_original['时间'], df_original['速度'], 
                linewidth=2, color='red', alpha=0.7, label='原始速度', linestyle='--')
        plt.plot(df_smoothed['时间'], df_smoothed['速度'], 
                linewidth=1.5, color='blue', alpha=0.8, label='平滑后速度')
    
    plt.title('速度曲线对比图', fontsize=14)
    plt.xlabel('时间')
    plt.ylabel('速度')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(output_image, dpi=300, bbox_inches='tight')
    print(f"对比图已保存到: {output_image}")
    plt.show()

def main():
    """主函数"""
    input_csv = 'navigation_time_speed.csv'
    output_csv = 'navigation_time_speed_smoothed.csv'
    
    print("开始速度平滑处理...")
    print("=" * 50)
    
    try:
        # 处理速度平滑
        df_original, df_smoothed = process_speed_smoothing(
            input_csv, output_csv, 
            transition_points=3,  # 过渡点数量
            window_size=5         # 移动平均窗口大小
        )
        
        print("\n" + "=" * 50)
        print("绘制对比图...")
        
        # 绘制对比图
        plot_comparison(df_original, df_smoothed)
        
        print("\n处理完成！")
        print(f"原始数据: {len(df_original)} 个点")
        print(f"平滑数据: {len(df_smoothed)} 个点")
        
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_csv}")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

if __name__ == "__main__":
    main()
