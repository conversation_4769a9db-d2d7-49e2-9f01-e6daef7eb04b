#!/usr/bin/env python3
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

def simple_plot(csv_file='navigation_time_speed.csv'):
    # 读取数据
    df = pd.read_csv(csv_file, header=None, names=['时间', '速度'])
    
    print(f"数据点数: {len(df)}")

    if len(df) > 2000:
        sample_rate = len(df) // 2000
        df_plot = df.iloc[::sample_rate]
    else:
        df_plot = df
    
    # 绘图
    plt.figure(figsize=(12, 6))
    plt.plot(df_plot['时间'], df_plot['速度'], linewidth=1, color='blue')
    plt.title('导航路线时间-速度变化图', fontsize=14)
    plt.xlabel('时间')
    plt.ylabel('速度')
    plt.grid(True, alpha=0.3)
    
    plt.text(0.02, 0.98, f'平均速度: {df["速度"].mean():.1f}', 
             transform=plt.gca().transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('time_speed_chart.png', dpi=200, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    simple_plot()
