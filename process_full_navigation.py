#!/usr/bin/env python3

import json
import csv

def process_full_navigation_data(json_file, output_csv):
    
    with open(json_file, 'r', encoding='utf-8') as f:
        navigation_data = json.load(f)
    
    csv_data = []
    current_time = 1  # 时间从1开始
    processed_segments = 0
    skipped_segments = 0
    
    print(f"总共 {len(navigation_data)} 个路段")
    
    for i, segment in enumerate(navigation_data):
        link_id = segment.get('linkId')
        travel_time = segment.get('travelTime', 0)
        link_speed = segment.get('linkSpeed', 0)
        road_name = segment.get('roadName', '未知')
        
        if travel_time == 0:
            skipped_segments += 1
            continue
        
        processed_segments += 1
        
        num_rows = travel_time // 100
        
        for j in range(num_rows):
            csv_data.append([current_time, link_speed])
            current_time += 1
    
    with open(output_csv, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerows(csv_data)

def main():
    json_file = 'navigation.json'
    output_csv = 'navigation_time_speed.csv'
    
    try:
        process_full_navigation_data(json_file, output_csv)
        print(f"\n处理完成！输出文件: {output_csv}")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

if __name__ == "__main__":
    main()
